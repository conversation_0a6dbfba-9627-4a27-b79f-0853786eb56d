import CryptoJS from 'crypto-js'

function WXBizDataCrypt(appId, sessionKey) {
  this.appId = appId
  this.sessionKey = sessionKey
}

WXBizDataCrypt.prototype.decryptData = function (encryptedData, iv) {
  try {
    // 验证输入参数
    if (!encryptedData || !iv || !this.sessionKey) {
      throw new Error('Missing required parameters: encryptedData, iv, or sessionKey')
    }

    console.log('Decryption inputs:', {
      sessionKey: this.sessionKey,
      encryptedData: encryptedData.substring(0, 20) + '...',
      iv: iv,
      appId: this.appId
    })

    // 使用 crypto-js 进行解密
    var sessionKey = CryptoJS.enc.Base64.parse(this.sessionKey)
    var ivParsed = CryptoJS.enc.Base64.parse(iv)

    console.log('Parsed sessionKey length:', sessionKey.words.length)
    console.log('Parsed IV length:', ivParsed.words.length)

    // 解密 - 直接使用 encryptedData 字符串，让 crypto-js 处理 base64 解码
    var decrypted = CryptoJS.AES.decrypt(
      encryptedData,
      sessionKey,
      {
        iv: ivParsed,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7
      }
    )

    console.log('Decrypted object:', decrypted)

    // 转换为 UTF-8 字符串
    var decryptedString = decrypted.toString(CryptoJS.enc.Utf8)
    console.log('Decrypted string:', decryptedString)

    if (!decryptedString) {
      throw new Error('Decryption failed - empty result')
    }

    var decoded = JSON.parse(decryptedString)
    console.log('Decoded JSON:', decoded)

    // 验证 watermark
    if (!decoded.watermark || !decoded.watermark.appid) {
      throw new Error('Invalid decrypted data - missing watermark')
    }

    if (decoded.watermark.appid !== this.appId) {
      throw new Error(`App ID mismatch: expected ${this.appId}, got ${decoded.watermark.appid}`)
    }

    return decoded

  } catch (err) {
    console.error('Decryption error:', err.message)
    console.error('Error stack:', err.stack)
    throw new Error(`Decryption failed: ${err.message}`)
  }
}

export default WXBizDataCrypt
