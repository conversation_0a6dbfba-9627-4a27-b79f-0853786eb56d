<template>
  <div class="app">
  </div>
</template>

<script setup>
// var WXBizDataCrypt = require('../utils/index')
import WXBizDataCrypt from '../utils/index'
console.log('WXBizDataCrypt', WXBizDataCrypt)

var appId = 'wxa4a449ac8f266f01'
var sessionKey = 'tiihtNczf5v6AKRyjwEUhQ=='
var encryptedData = 
	'SxVnnZTnH/c3MtIJkCQr5a4v/fRu8WKT0teRBNIL1gck5gwGx/Vd8kUE3Pc5o820PXT8ohVYC6WspsIHMIXJA0wzRxbpQIWt9YuRYb+tpgU='
var iv = '1TPs+KGh/8PugPA9qjymPQ=='

var pc = new WXBizDataCrypt(appId, sessionKey)

var data = pc.decryptData(encryptedData , iv)

console.log('解密后 data: ', data)
// 解密后的数据为
//
// data = {
//   "nickName": "Band",
//   "gender": 1,
//   "language": "zh_CN",
//   "city": "Guangzhou",
//   "province": "Guangdong",
//   "country": "CN",
//   "avatarUrl": "http://wx.qlogo.cn/mmopen/vi_32/aSKcBBPpibyKNicHNTMM0qJVh8Kjgiak2AHWr8MHM4WgMEm7GFhsf8OYrySdbvAMvTsw3mo8ibKicsnfN5pRjl1p8HQ/0",
//   "unionId": "ocMvos6NjeKLIBqg5Mr9QjxrP1FA",
//   "watermark": {
//     "timestamp": 1477314187,
//     "appid": "wx4f4bc4dec97d474b"
//   }
// }

</script>

<style>
</style> 